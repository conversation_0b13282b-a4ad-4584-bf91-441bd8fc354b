# 🎨 Optimasi CSS

Dokumen ini menjelaskan strategi optimasi CSS yang diimplementasikan dalam proyek portfolio Astro ini, khususnya untuk menangani CSS pada halaman yang dinonaktifkan.

## 🔍 Masalah

Ketika halaman dinonaktifkan melalui konfigurasi (`enabled: false`), CSS untuk halaman tersebut masih dirender dan dibuild, menyebabkan:

- Ukuran bundle CSS yang lebih besar dari yang diperlukan
- Peringatan "unused CSS" pada alat audit performa seperti Lighthouse
- Waktu loading yang lebih lama karena browser harus memproses CSS yang tidak digunakan

Contoh peringatan Lighthouse:
```
Reduce unused CSS Est savings of 12 KiB
Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content to decrease bytes consumed by network activity.
```

## 💡 Solusi

Proyek ini mengimplementasikan beberapa strategi untuk mengatasi masalah tersebut:

### 1. Conditional CSS Rendering

CSS inline pada halaman yang dinonaktifkan tidak akan dirender sama sekali. Ini diimplementasikan dengan menambahkan kondisi pada blok `<style>` dan `<script>` di file halaman.

Contoh implementasi di `about.astro`:

```astro
{isPageEnabled('about') && (
  <style>
    .about-page {
      /* CSS styles */
    }
  </style>
)}
```

### 2. CSS Code Splitting dengan Vite

Konfigurasi Vite di `astro.config.mjs` telah dimodifikasi untuk memisahkan CSS halaman yang dinonaktifkan ke dalam chunk terpisah. Ini memastikan bahwa CSS untuk halaman yang dinonaktifkan tidak akan dimasukkan dalam bundle utama.

```javascript
manualChunks: (id) => {
  // Memisahkan CSS untuk halaman About ke chunk terpisah
  if (id.includes('/components/About/') || 
      id.includes('/components/CareerSection/') || 
      id.includes('/components/PortfolioSection/') || 
      id.includes('/pages/about.astro')) {
    return 'about';
  }
}
```

### 3. Validasi Akses Halaman

Sistem validasi halaman mencegah halaman yang dinonaktifkan dari proses build. Fungsi `validatePageAccess()` memeriksa status halaman dan mengembalikan respons 404 jika halaman dinonaktifkan.

```typescript
// utils/pages.ts
export function validatePageAccess(pageName: string): void {
  if (!isPageEnabled(pageName)) {
    throw new Error(`Page ${pageName} is disabled`);
  }
}
```

## 📊 Hasil

Dengan implementasi optimasi ini, ketika halaman `/about` dinonaktifkan:

1. CSS untuk halaman `/about` tidak akan dirender atau dibuild
2. Ukuran bundle CSS berkurang secara signifikan (estimasi penghematan ~12 KiB)
3. Tidak ada peringatan "unused CSS" untuk file `about.DCUpEDrw.css`
4. Performa loading halaman meningkat

## 🔧 Cara Menggunakan

Untuk menonaktifkan halaman dan CSS-nya:

1. Edit file `src/config/components.ts`
2. Set `enabled: false` untuk halaman yang ingin dinonaktifkan

```typescript
// src/config/components.ts
pages: {
  about: {
    enabled: false // Menonaktifkan halaman dan CSS-nya
  }
}
```

## 🔄 Integrasi dengan Sistem Halaman Kondisional

Optimasi CSS ini terintegrasi dengan sistem halaman kondisional yang dijelaskan dalam `CONDITIONAL_PAGES_SYSTEM.md`. Sistem ini memastikan bahwa:

1. Halaman yang dinonaktifkan tidak akan dirender atau dibuild
2. CSS untuk halaman yang dinonaktifkan tidak akan dimasukkan dalam bundle
3. Link navigasi ke halaman yang dinonaktifkan tidak akan ditampilkan
4. Sitemap akan diperbarui secara otomatis untuk mengecualikan halaman yang dinonaktifkan

## 🚀 Optimasi Lebih Lanjut

Beberapa optimasi CSS tambahan yang diimplementasikan dalam proyek ini:

1. **Tailwind CSS Purging**: Konfigurasi Tailwind memastikan hanya CSS yang digunakan yang dimasukkan dalam build
2. **Lazy Loading Komponen**: Komponen yang berat hanya dimuat saat diperlukan
3. **Reduced Motion Support**: CSS alternatif untuk pengguna yang memilih reduced motion

Lihat `PERFORMANCE_OPTIMIZATIONS.md` untuk detail lebih lanjut tentang optimasi performa lainnya dalam proyek ini.