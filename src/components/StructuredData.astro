---
import { seoConfig } from '../config/components';
import { generateStructuredData, type SEOData } from '../utils/seo';

export interface Props {
  type?: 'website' | 'article' | 'person' | 'organization';
  title?: string;
  description?: string;
  url?: string;
  image?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  keywords?: string[];
  section?: string;
  tags?: string[];
  additionalData?: any;
}

const {
  type = 'website',
  title = seoConfig.site.title,
  description = seoConfig.site.description,
  url = seoConfig.site.url,
  image = seoConfig.images.default,
  author = seoConfig.author.name,
  publishedTime,
  modifiedTime,
  keywords = [],
  section,
  tags = [],
  additionalData = {}
} = Astro.props;

const fullUrl = url.startsWith('http') ? url : `${seoConfig.site.url}${url}`;
const fullImage = image.startsWith('http') ? image : `${seoConfig.site.url}${image}`;

// Create SEO data object
const seoData: SEOData = {
  title,
  description,
  canonical: fullUrl,
  image: fullImage,
  type,
  keywords,
  author,
  publishedTime,
  modifiedTime,
  section,
  tags
};

// Generate structured data using utility function
const structuredData = generateStructuredData(seoData, additionalData);

// Additional breadcrumb data for articles
const breadcrumbData = type === 'article' ? {
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": seoConfig.site.url
    },
    {
      "@type": "ListItem",
      "position": 2,
      "name": "Blog",
      "item": `${seoConfig.site.url}/blog`
    },
    {
      "@type": "ListItem",
      "position": 3,
      "name": title,
      "item": fullUrl
    }
  ]
} : null;
---

<!-- Main Structured Data -->
<script type="application/ld+json" set:html={JSON.stringify(structuredData)} />

<!-- Breadcrumb Structured Data for Articles -->
{breadcrumbData && (
  <script type="application/ld+json" set:html={JSON.stringify(breadcrumbData)} />
)}

<!-- Organization Structured Data (Global) -->
{type === 'website' && (
  <script type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": seoConfig.organization.name,
    "url": seoConfig.organization.url,
    "logo": {
      "@type": "ImageObject",
      "url": seoConfig.organization.logo
    },
    "sameAs": seoConfig.organization.sameAs,
    "contactPoint": {
      "@type": "ContactPoint",
      "email": seoConfig.author.email,
      "contactType": "customer service"
    }
  })} />
)}
