---
// Performance optimization component for critical CSS and resource loading
---

<!-- Performance optimization script -->
<script>
  // Critical performance optimizations
  (function() {
    // Function to safely add loading classes to body
    function initializeBodyClasses() {
      if (document.body) {
        document.body.classList.add('loading');
      }
    }

    // Function to remove loading class when ready
    function markAsLoaded() {
      if (document.body) {
        document.body.classList.remove('loading');
        document.body.classList.add('loaded');
      }
    }

    // Initialize body classes safely
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', function() {
        initializeBodyClasses();
        markAsLoaded();
      });
    } else {
      // DOM is already ready
      initializeBodyClasses();
      markAsLoaded();
    }
    
    // Defer non-critical CSS loading
    function loadDeferredStyles() {
      const addStylesNode = document.getElementById('deferred-styles');
      if (addStylesNode && document.body) {
        const replacement = document.createElement('div');
        replacement.innerHTML = addStylesNode.textContent;
        document.body.appendChild(replacement);
        addStylesNode.parentElement.removeChild(addStylesNode);
      }
    }
    
    // Load deferred styles after page load
    if (window.addEventListener) {
      window.addEventListener('load', loadDeferredStyles, false);
    } else if (window.attachEvent) {
      window.attachEvent('onload', loadDeferredStyles);
    }
    
    // Optimize images with lazy loading
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.classList.remove('lazy');
              observer.unobserve(img);
            }
          }
        });
      });
      
      // Observe all lazy images when DOM is ready
      function observeLazyImages() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => imageObserver.observe(img));
      }
      
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', observeLazyImages);
      } else {
        observeLazyImages();
      }
    }
    
    // Preload critical resources
    function preloadCriticalResources() {
      // Preload hero image if exists
      const heroImg = document.querySelector('.hero-image');
      if (heroImg && heroImg.dataset.src) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = heroImg.dataset.src;
        document.head.appendChild(link);
      }
    }
    
    // Run preload after DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', preloadCriticalResources);
    } else {
      preloadCriticalResources();
    }
  })();
</script>

<!-- Performance monitoring script -->
<script>
  // Performance monitoring for LCP (Largest Contentful Paint)
  if (window.location.hostname === 'localhost') {
    try {
      const lcpObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        if (entries.length > 0) {
          const lcpEntry = entries[entries.length - 1];
          const lcpTime = lcpEntry.startTime;
          
          // Log slow LCP in development
          if (lcpTime > 2500) {
            console.log(`Slow LCP detected: ${lcpTime.toFixed(2)}ms`);
          }
        }
      });
      
      // Only observe LCP entries
      lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });
    } catch (e) {
      console.error('LCP monitoring error:', e);
    }
    
    // Separate observer for CLS (Cumulative Layout Shift)
    try {
      const clsObserver = new PerformanceObserver((entryList) => {
        let clsValue = 0;
        
        for (const entry of entryList.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        }
        
        // Log high CLS in development
        if (clsValue > 0.1) {
          console.log(`High CLS detected: ${clsValue.toFixed(3)}`);
        }
      });
      
      // Only observe layout-shift entries if supported
      if (PerformanceObserver.supportedEntryTypes?.includes('layout-shift')) {
        clsObserver.observe({ type: 'layout-shift', buffered: true });
      }
    } catch (e) {
      console.error('CLS monitoring error:', e);
    }
  }
</script>

<!-- Deferred styles placeholder -->
<noscript id="deferred-styles">
  <!-- Non-critical CSS will be loaded here -->
</noscript>

<!-- Resource hints for better performance -->
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//fonts.gstatic.com">
<link rel="dns-prefetch" href="//github.com">
<link rel="dns-prefetch" href="//linkedin.com">

<!-- Preload critical resources -->
<!-- Note: client.js preload removed as it was causing 404 errors -->
