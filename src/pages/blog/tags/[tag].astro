---
import { getCollection } from 'astro:content';
import Layout from '../../../layouts/Layout.astro';
import { ThemeProvider } from '../../../components/ThemeProvider';
import LayoutWrapper from '../../../components/LayoutWrapper';
import Header from '../../../components/Header';
import BlogList from '../../../components/BlogList';
import Footer from '../../../components/Footer';
import { calculateReadingTime } from '../../../utils/readingTime';

export async function getStaticPaths() {
  const posts = await getCollection('posts');
  const publishedPosts = posts.filter(post => !post.data.draft && !post.data.hidden);
  
  // Get all unique tags
  const allTags = Array.from(new Set(publishedPosts.flatMap(post => post.data.tags || [])));
  
  return allTags.map((tag) => ({
    params: { tag },
    props: { tag, allPosts: publishedPosts },
  }));
}

const { tag, allPosts } = Astro.props;

// Filter posts by tag
const tagPosts = allPosts.filter(post => post.data.tags?.includes(tag));

// Calculate reading times
const postsWithReadingTime = tagPosts.map(post => {
  const readingTimeResult = calculateReadingTime(post.body || '', {
    wordsPerMinute: 200,
    includeCodeBlocks: true,
    includeImages: true
  });
  return { ...post, readingTime: readingTimeResult.minutes };
}).sort((a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime());

// Generate metadata
const postCount = postsWithReadingTime.length;
const description = `Explore ${postCount} blog posts tagged with "${tag}". Discover articles about ${tag} and related topics by Febryan Ramadhan.`;
const title = `Posts tagged "${tag}" | Febryan Blog`;
---

<Layout 
  title={title}
  description={description}
  canonical={`https://febryan.web.id/blog/tags/${tag}`}
>
  <ThemeProvider client:load>
    <LayoutWrapper client:load>
      <div class="min-h-screen bg-white dark:bg-neutral-900">
        <Header client:load />
        
        <main class="max-w-7xl mx-auto px-4 pt-24 pb-16">
          {/* Tag Header */}
          <div class="text-center mb-12">
            <div class="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/30 
              text-blue-800 dark:text-blue-300 rounded-full text-sm font-medium mb-4">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
              </svg>
              Tag: {tag}
            </div>
            
            <h1 class="text-4xl md:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Posts tagged "{tag}"
            </h1>
            
            <p class="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl mx-auto mb-6">
              {description}
            </p>

            {/* Navigation Links */}
            <div class="flex flex-wrap justify-center gap-4">
              <a 
                href="/blog" 
                class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium
                  text-neutral-600 dark:text-neutral-400 hover:text-blue-600 dark:hover:text-blue-400
                  bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700
                  rounded-lg transition-colors"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                All Posts
              </a>
              
              <a 
                href="/blog/tags" 
                class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium
                  text-neutral-600 dark:text-neutral-400 hover:text-blue-600 dark:hover:text-blue-400
                  bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700
                  rounded-lg transition-colors"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
                Browse All Tags
              </a>
            </div>
          </div>

          {/* Posts List */}
          <BlogList
            posts={postsWithReadingTime}
            selectedTag={tag}
            postsPerPage={9}
            client:load
          />
        </main>
        
        <Footer client:load />
      </div>
    </LayoutWrapper>
  </ThemeProvider>
</Layout>
