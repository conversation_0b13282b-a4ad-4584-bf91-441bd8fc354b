---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import { ThemeProvider } from '../../components/ThemeProvider';
import LayoutWrapper from '../../components/LayoutWrapper';
import Header from '../../components/Header';
import BlogList from '../../components/BlogList';
import BlogPageClient from '../../components/BlogPageClient';
import Footer from '../../components/Footer';
import { calculateReadingTime } from '../../utils/readingTime';
import { generateBlogListingSEO } from '../../utils/seo';

// Get all blog posts
const posts = await getCollection('posts');
const publishedPosts = posts
  .filter(post => !post.data.draft && !post.data.hidden)
  .sort((a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime());

// Calculate reading times with improved algorithm
const postsWithReadingTime = publishedPosts.map(post => {
  const readingTimeResult = calculateReadingTime(post.body || '', {
    wordsPerMinute: 200,
    includeCodeBlocks: true,
    includeImages: true
  });
  return { ...post, readingTime: readingTimeResult.minutes };
});

// Generate SEO data for blog listing page
const blogSEO = generateBlogListingSEO(postsWithReadingTime);
---

<Layout
  title={blogSEO.title}
  description={blogSEO.description}
  canonical={blogSEO.canonical}
  type={blogSEO.type}
  keywords={blogSEO.keywords}
>
  <ThemeProvider client:load>
    <LayoutWrapper client:load>
      <div class="min-h-screen bg-white dark:bg-neutral-900">
        <BlogPageClient posts={postsWithReadingTime} client:load />
        <Footer client:load />
      </div>
    </LayoutWrapper>
  </ThemeProvider>
</Layout>
