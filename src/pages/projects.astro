---
import Layout from '../layouts/Layout.astro';
import { ThemeProvider } from '../components/ThemeProvider';
import LayoutWrapper from '../components/LayoutWrapper';
import Header from '../components/Header';
import ProjectsWithCategories from '../components/ProjectsWithCategories';
import SkillsSection from '../components/SkillsSection';
import CertificationsSection from '../components/CertificationsSection';
import FloatingNavigation from '../components/FloatingNavigation';
import Footer from '../components/Footer';
import { generateSEO } from '../utils/seo';
import { componentConfig } from '../config/components';
import { validatePageAccess } from '../utils/pages';

const { pages } = componentConfig;

// Generate SEO data for projects page
const projectsSEO = generateSEO({
  title: 'Projects - Febryan Ramadhan',
  description: 'Explore my portfolio of cloud engineering, DevOps, and web development projects. Discover the technologies and solutions I\'ve built.',
  type: 'website',
  keywords: ['projects', 'portfolio', 'cloud engineering', 'devops', 'web development', 'aws', 'kubernetes']
});

// Validate page access - this will throw error if page is disabled
try {
  validatePageAccess('projects');
} catch (error) {
  // Page is disabled, return 404
  return new Response(null, {
    status: 404,
    statusText: 'Page not found'
  });
}
---

<Layout
  title={projectsSEO.title}
  description={projectsSEO.description}
  canonical={projectsSEO.canonical}
  type={projectsSEO.type}
  keywords={projectsSEO.keywords}
>
  <ThemeProvider client:load>
    <LayoutWrapper client:load>
      <div class="min-h-screen bg-white dark:bg-neutral-900">
        <Header client:load />

        <main class="max-w-7xl mx-auto px-4 py-4 mb-10">
          <!-- Page Header -->
          <section class="pt-24 pb-16">
            <div class="text-center max-w-4xl mx-auto">
              <h1 class="text-4xl md:text-6xl font-bold bg-clip-text text-transparent
                bg-gradient-to-r from-blue-600 to-purple-600
                dark:from-blue-400 dark:to-purple-400 mb-6 leading-tight pb-3">
                Febryan Ramadhan Portfolio & Projects
              </h1>
              <p class="text-lg md:text-xl text-neutral-600 dark:text-neutral-400 leading-relaxed">
                Explore my journey in cloud infrastructure, DevOps automation, and scalable solutions.
                Each project showcases modern engineering practices and innovative problem-solving.
              </p>
            </div>
          </section>

          <!-- Projects with Categories -->
          <section id="projects" class="py-16">
            <ProjectsWithCategories client:load />
          </section>

          <!-- Skills Section -->
          <SkillsSection client:load />

          <!-- Certifications Section -->
          <CertificationsSection client:load />

          <!-- Call to Action -->
          {pages.projects.cta.enabled && (
            <section id="cta" class="py-16">
              <div class="max-w-4xl mx-auto text-center">
                <div class={`p-8 md:p-12 rounded-3xl ${
                  pages.projects.cta.backgroundStyle === 'gradient'
                    ? 'bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white'
                    : pages.projects.cta.backgroundStyle === 'pattern'
                    ? 'bg-neutral-900 dark:bg-neutral-800 text-white'
                    : 'bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-700 text-neutral-900 dark:text-neutral-100'
                }`}>
                  <h2 class="text-3xl md:text-4xl font-bold mb-4">
                    {pages.projects.cta.title}
                  </h2>
                  <p class={`text-lg md:text-xl mb-8 ${
                    pages.projects.cta.backgroundStyle === 'minimal'
                      ? 'text-neutral-600 dark:text-neutral-400'
                      : 'text-white/90'
                  }`}>
                    {pages.projects.cta.subtitle}
                  </p>
                  <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href={pages.projects.cta.primaryAction.link} class={`inline-flex items-center gap-2 px-8 py-4 rounded-2xl font-semibold transition-colors ${
                      pages.projects.cta.backgroundStyle === 'minimal'
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600'
                        : 'bg-white text-neutral-900 hover:bg-neutral-100'
                    }`}>
                      <span>{pages.projects.cta.primaryAction.text}</span>
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                      </svg>
                    </a>
                    <a href={pages.projects.cta.secondaryAction.link} class={`inline-flex items-center gap-2 px-8 py-4 rounded-2xl font-semibold transition-all ${
                      pages.projects.cta.backgroundStyle === 'minimal'
                        ? 'border-2 border-neutral-300 dark:border-neutral-600 text-neutral-700 dark:text-neutral-300 hover:border-neutral-400 dark:hover:border-neutral-500'
                        : 'border-2 border-white/30 text-white hover:border-white/50 hover:bg-white/10'
                    }`}>
                      <span>{pages.projects.cta.secondaryAction.text}</span>
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </section>
          )}
        </main>

        <!-- Floating Navigation -->
        <!-- <FloatingNavigation client:load /> -->

        <!-- Footer -->
        <Footer client:load />
      </div>
    </LayoutWrapper>
  </ThemeProvider>
</Layout>

<style>
  /* Custom styles for projects page */
  .projects-page {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%);
  }
  
  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }
  
  /* Section spacing */
  section {
    scroll-margin-top: 80px;
  }
  
  /* Project card hover effects */
  .project-card {
    transition: all 0.3s ease;
  }
  
  .project-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
</style>

<script>
  // Add interactive features for projects page
  document.addEventListener('DOMContentLoaded', () => {
    // Filter functionality (if needed)
    const filterButtons = document.querySelectorAll('[data-filter]');
    const projectCards = document.querySelectorAll('[data-category]');
    
    filterButtons.forEach(button => {
      button.addEventListener('click', () => {
        const filter = button.getAttribute('data-filter');
        
        // Update active button
        filterButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        
        // Filter projects
        projectCards.forEach(card => {
          const category = card.getAttribute('data-category');
          const htmlCard = card as HTMLElement;
          if (filter === 'all' || category === filter) {
            htmlCard.style.display = 'block';
            htmlCard.style.animation = 'fadeIn 0.5s ease';
          } else {
            htmlCard.style.display = 'none';
          }
        });
      });
    });
    
    // Lazy loading for project images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          const dataSrc = img.getAttribute('data-src');
          if (dataSrc) {
            img.src = dataSrc;
            img.removeAttribute('data-src');
          }
          imageObserver.unobserve(img);
        }
      });
    });
    
    images.forEach(img => imageObserver.observe(img));
  });
</script>
