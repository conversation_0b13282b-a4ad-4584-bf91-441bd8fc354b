---
import Layout from '../layouts/Layout.astro';
import { ThemeProvider } from '../components/ThemeProvider';
import LayoutWrapper from '../components/LayoutWrapper';
import Header from '../components/Header';
import About from '../components/About';
import CareerSection from '../components/CareerSection';
import PortfolioSection from '../components/PortfolioSection';
import Contact from '../components/Contact';
import FloatingNavigation from '../components/FloatingNavigation';
import Footer from '../components/Footer';
import { generateSEO } from '../utils/seo';
import { componentConfig } from '../config/components';
import { isPageEnabled, validatePageAccess } from '../utils/pages';

const { pages } = componentConfig;

// Generate SEO data for about page
const aboutSEO = generateSEO({
  title: 'About - Febryan Ramadhan',
  description: 'Learn more about <PERSON><PERSON> - Cloud Engineer, DevOps specialist, and web developer. Discover my experience, education, skills, and professional journey.',
  type: 'person',
  keywords: ['about', 'experience', 'education', 'skills', 'cloud engineer', 'devops', 'career']
});

// Validate page access - this will throw error if page is disabled
// This prevents the page from being built if disabled
try {
  validatePageAccess('about');
} catch (error) {
  // Page is disabled, return 404
  return new Response(null, {
    status: 404,
    statusText: 'Page not found'
  });
}
---

<Layout
  title={aboutSEO.title}
  description={aboutSEO.description}
  canonical={aboutSEO.canonical}
  type={aboutSEO.type}
  keywords={aboutSEO.keywords}
>
  <ThemeProvider client:load>
    <LayoutWrapper client:load>
      <div class="min-h-screen bg-white dark:bg-neutral-900">
        <Header client:load />

        <main class="max-w-7xl mx-auto px-4 py-4 mb-10">
          <!-- Page Header -->
          <section class="pt-24 pb-16">
            <div class="text-center max-w-4xl mx-auto">
              <h1 class="text-4xl md:text-6xl font-bold bg-clip-text text-transparent
                bg-gradient-to-r from-blue-600 to-purple-600
                dark:from-blue-400 dark:to-purple-400 mb-6">
                About Me
              </h1>
              <p class="text-lg md:text-xl text-neutral-600 dark:text-neutral-400 leading-relaxed">
                Get to know more about my professional journey, experience, and the passion that drives my work in technology.
              </p>
            </div>
          </section>

          {pages.about.sections.story && (
            <!-- Personal Story Section -->
            <section id="story" class="py-16 sm:py-24">
              <About client:load />
            </section>
          )}

          {pages.about.sections.experience && (
            <!-- Experience & Education Section -->
            <section id="experience" class="py-16 sm:py-24">
              <CareerSection client:load />
            </section>
          )}

          {pages.about.sections.skills && (
            <!-- Skills & Portfolio Section -->
            <section id="skills" class="py-16 sm:py-24">
              <PortfolioSection client:load />
            </section>
          )}

          {pages.about.sections.stats && (
            <!-- Additional Stats or Achievements Section -->
            <section id="achievements" class="py-16 sm:py-24">
              <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-12">
                  Professional Highlights
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                  <div class="p-6 bg-white/80 dark:bg-neutral-800/80 rounded-2xl shadow-lg border border-neutral-200 dark:border-neutral-700">
                    <div class="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">4+</div>
                    <div class="text-neutral-600 dark:text-neutral-400">Years Experience</div>
                  </div>
                  <div class="p-6 bg-white/80 dark:bg-neutral-800/80 rounded-2xl shadow-lg border border-neutral-200 dark:border-neutral-700">
                    <div class="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">15+</div>
                    <div class="text-neutral-600 dark:text-neutral-400">Projects Completed</div>
                  </div>
                  <div class="p-6 bg-white/80 dark:bg-neutral-800/80 rounded-2xl shadow-lg border border-neutral-200 dark:border-neutral-700">
                    <div class="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">100+</div>
                    <div class="text-neutral-600 dark:text-neutral-400">Deployments</div>
                  </div>
                </div>
              </div>
            </section>
          )}

          <!-- Contact Section -->
          <section id="contact" class="py-16 sm:py-24">
            <Contact client:load />
          </section>
        </main>

        <!-- Floating Navigation -->
        <FloatingNavigation client:load />

        <!-- Footer -->
        <Footer client:load />
      </div>
    </LayoutWrapper>
  </ThemeProvider>
</Layout>

<style>
  /* Custom styles for about page */
  .about-page {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%);
  }
  
  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }
  
  /* Section spacing */
  section {
    scroll-margin-top: 80px;
  }
</style>

<script>
  // Add smooth scrolling and section highlighting
  document.addEventListener('DOMContentLoaded', () => {
    // Intersection Observer for section highlighting
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('nav a[href^="#"]');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const id = entry.target.getAttribute('id');
          navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${id}`) {
              link.classList.add('active');
            }
          });
        }
      });
    }, {
      threshold: 0.3,
      rootMargin: '-80px 0px -80px 0px'
    });
    
    sections.forEach(section => observer.observe(section));
  });
</script>
