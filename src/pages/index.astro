---
import Layout from '../layouts/Layout.astro';
import { ThemeProvider } from '../components/ThemeProvider';
import LayoutWrapper from '../components/LayoutWrapper';
import Header from '../components/Header';
import Hero from '../components/Hero';
import Story<PERSON><PERSON>ney from '../components/StoryJourney';
import BentoGrid from '../components/BentoGrid';
import FeaturedWork from '../components/FeaturedWork';
import BlogCTA from '../components/BlogCTA';
import CTASection from '../components/CTASection';
import FloatingNavigation from '../components/FloatingNavigation';
import FloatingBlogButton from '../components/FloatingBlogButton';
import Footer from '../components/Footer';
import { generateSEO } from '../utils/seo';
import { componentConfig } from '../config/components';
import { shouldShowAboutSection, shouldShowContactSection } from '../utils/pages';

const { homepage } = componentConfig;

// Generate SEO data for homepage
const homepageSEO = generateSEO({
  type: 'website',
  keywords: ['portfolio', 'cloud engineer', 'devops specialist', 'personal website', '2025 design', 'modern portfolio']
});
---

<Layout
  title={homepageSEO.title}
  description={homepageSEO.description}
  canonical={homepageSEO.canonical}
  type={homepageSEO.type}
  keywords={homepageSEO.keywords}
>
  <ThemeProvider client:load>
    <LayoutWrapper client:load>
      <div class="min-h-screen bg-white dark:bg-neutral-900">
        <Header client:load />

      <main class="max-w-7xl mx-auto px-4 pt-20 pb-4 mb-10">
        <!-- Hero Section - Enhanced 2025 Design -->
        <section id="home" class="min-h-screen flex items-center justify-center -mt-20 px-4">
          <div class="w-full max-w-6xl mx-auto">
            <Hero client:load />
          </div>
        </section>

        {homepage.sections.story && (
          <!-- Story Journey Section -->
          <section id="story" class="relative">
            <StoryJourney client:only="react" />
          </section>
        )}

        {homepage.sections.bentoGrid && shouldShowAboutSection() && (
          <!-- Bento Grid Section - Only show if about page is enabled -->
          <section id="about" class="relative">
            <BentoGrid client:only="react" />
          </section>
        )}

        {homepage.sections.featuredWork && (
          <!-- Featured Work Section -->
          <section id="work" class="relative">
            <FeaturedWork client:only="react" />
          </section>
        )}

        <!-- Blog CTA Section -->
        <!-- <section id="blog-cta" class="relative">
          <BlogCTA client:load />
        </section> -->

        {homepage.sections.cta && (
          <!-- CTA Section -->
          <section id="contact" class="relative">
            <CTASection client:only="react" />
          </section>
        )}
      </main>

      <!-- Floating Navigation -->
      <FloatingNavigation client:only="react" />

      <!-- Floating Blog Button -->
      <!-- <FloatingBlogButton client:load /> -->

        <!-- Footer -->
        <Footer client:load />
      </div>
    </LayoutWrapper>
  </ThemeProvider>
</Layout>
