---
import Layout from '../layouts/Layout.astro';
import { ThemeProvider } from '../components/ThemeProvider';
import Header from '../components/Header';
---

<Layout 
  title="404 - Page Not Found | Febryan Portfolio"
  description="The page you're looking for doesn't exist. Return to the homepage or explore other sections."
>
  <ThemeProvider client:load>
    <div class="min-h-screen bg-white dark:bg-neutral-900">
      <Header client:load />
      
      <main class="max-w-4xl mx-auto px-4 py-8 mt-16">
        <div class="text-center py-20">
          {/* 404 Animation */}
          <div class="mb-8">
            <div class="text-8xl md:text-9xl font-bold text-neutral-200 dark:text-neutral-800 mb-4">
              404
            </div>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mb-8"></div>
          </div>

          {/* Error Message */}
          <h1 class="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Page Not Found
          </h1>
          <p class="text-lg text-neutral-600 dark:text-neutral-400 mb-8 max-w-2xl mx-auto">
            The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.
          </p>

          {/* Quick Navigation */}
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-12 max-w-4xl mx-auto">
            <a 
              href="/"
              class="group p-6 bg-white/80 dark:bg-neutral-800/80 rounded-xl shadow-sm border border-neutral-200 dark:border-neutral-700 hover:shadow-md transition-all duration-300"
            >
              <div class="flex items-center mb-3">
                <svg class="w-6 h-6 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <span class="font-semibold text-neutral-900 dark:text-neutral-100">Home</span>
              </div>
              <p class="text-sm text-neutral-600 dark:text-neutral-400">Back to homepage</p>
            </a>

            <a 
              href="/#about"
              class="group p-6 bg-white/80 dark:bg-neutral-800/80 rounded-xl shadow-sm border border-neutral-200 dark:border-neutral-700 hover:shadow-md transition-all duration-300"
            >
              <div class="flex items-center mb-3">
                <svg class="w-6 h-6 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="font-semibold text-neutral-900 dark:text-neutral-100">About</span>
              </div>
              <p class="text-sm text-neutral-600 dark:text-neutral-400">Learn about me</p>
            </a>

            <a 
              href="/#experience"
              class="group p-6 bg-white/80 dark:bg-neutral-800/80 rounded-xl shadow-sm border border-neutral-200 dark:border-neutral-700 hover:shadow-md transition-all duration-300"
            >
              <div class="flex items-center mb-3">
                <svg class="w-6 h-6 text-purple-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                </svg>
                <span class="font-semibold text-neutral-900 dark:text-neutral-100">Experience</span>
              </div>
              <p class="text-sm text-neutral-600 dark:text-neutral-400">My work history</p>
            </a>

            <a 
              href="/blog"
              class="group p-6 bg-white/80 dark:bg-neutral-800/80 rounded-xl shadow-sm border border-neutral-200 dark:border-neutral-700 hover:shadow-md transition-all duration-300"
            >
              <div class="flex items-center mb-3">
                <svg class="w-6 h-6 text-orange-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                <span class="font-semibold text-neutral-900 dark:text-neutral-100">Blog</span>
              </div>
              <p class="text-sm text-neutral-600 dark:text-neutral-400">Read my articles</p>
            </a>
          </div>

          {/* Search Suggestion */}
          <div class="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 max-w-md mx-auto">
            <h3 class="font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
              Looking for something specific?
            </h3>
            <p class="text-sm text-neutral-600 dark:text-neutral-400 mb-4">
              Try searching through my blog posts or check out my latest projects.
            </p>
            <a 
              href="/blog"
              class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              Browse Blog
            </a>
          </div>
        </div>
      </main>

      <!-- Footer -->
      <footer class="border-t border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900">
        <div class="max-w-3xl mx-auto px-4 py-3 text-center">
          <p class="text-sm text-neutral-600 dark:text-neutral-400">
            © {new Date().getFullYear()} | Febryan Ramadhan. All rights reserved.
          </p>
          <p class="flex items-center justify-center gap-2 mt-0.5 text-xs text-neutral-500 dark:text-neutral-500">
            <span>Built with Astro and Claude AI</span>
          </p>
        </div>
      </footer>
    </div>
  </ThemeProvider>
</Layout>
