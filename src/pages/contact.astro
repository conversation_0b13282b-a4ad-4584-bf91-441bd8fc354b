---
import Layout from '../layouts/Layout.astro';
import { ThemeProvider } from '../components/ThemeProvider';
import LayoutWrapper from '../components/LayoutWrapper';
import Header from '../components/Header';
import ContactComponent from '../components/Contact';
import FloatingNavigation from '../components/FloatingNavigation';
import Footer from '../components/Footer';
import { generateSEO } from '../utils/seo';
import { componentConfig } from '../config/components';
import { validatePageAccess } from '../utils/pages';

const { pages, navigation } = componentConfig;

// Validate page access - this will throw error if page is disabled
try {
  validatePageAccess('contact');
} catch (error) {
  // Page is disabled, return 404
  return new Response(null, {
    status: 404,
    statusText: 'Page not found'
  });
}

// Generate SEO data for contact page
const contactSEO = generateSEO({
  title: 'Contact',
  description: 'Get in touch with <PERSON><PERSON> for collaborations, opportunities, or just to say hello! Available for cloud engineering, DevOps, and web development projects.',
  canonical: '/contact',
  type: 'website',
  keywords: ['contact', 'collaboration', 'cloud engineer', 'devops', 'web developer', 'freelance', 'opportunities']
});
---

<Layout
  title={contactSEO.title}
  description={contactSEO.description}
  canonical={contactSEO.canonical}
  image={contactSEO.image}
  type={contactSEO.type}
  keywords={contactSEO.keywords}
>
  <ThemeProvider client:load>
    <LayoutWrapper>
      <!-- Header -->
      <Header client:load />

      <!-- Main Content -->
      <main class="max-w-7xl mx-auto px-4 py-4 mb-10">
        <!-- Contact Section -->
        <section id="contact" class="min-h-screen flex items-center justify-center -mt-16 px-4">
          <div class="w-full max-w-6xl mx-auto pt-20">
            <ContactComponent client:load />
          </div>
        </section>
      </main>

      <!-- Floating Navigation -->
      {navigation.floating.enabled && (
        <FloatingNavigation client:load />
      )}

      <!-- Footer -->
      <Footer client:load />
    </LayoutWrapper>
  </ThemeProvider>
</Layout>
