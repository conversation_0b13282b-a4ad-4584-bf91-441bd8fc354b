# 🚀 Projects Configuration Guide

## Konfigurasi <PERSON>aman Projects

File ini menjelaskan cara mengkonfigurasi halaman projects di portfolio website.

## 📁 File Konfigurasi

Konfigurasi projects diatur dalam file:
```
src/config/components.ts
```

## ⚙️ Opsi Konfigurasi

### Konfigurasi Dasar

```typescript
pages: {
  projects: {
    enabled: true,              // Enable/disable halaman projects
    showInNavigation: true,     // Tampilkan di menu navigasi
    layout: "grid",             // Layout: "grid", "masonry", "list"
    showFilters: true,          // Tampilkan filter kategori
  }
}
```

### Konfigurasi Kategori

```typescript
categories: [
  {
    id: "all",
    name: "All",
    description: "All projects and work",
    icon: "🚀",
    count: 9
  },
  {
    id: "cloud",
    name: "Cloud",
    description: "Cloud infrastructure and architecture",
    icon: "☁️",
    count: 3
  },
  // ... kategori lainnya
]
```

### Konfigurasi CTA (Call to Action)

```typescript
cta: {
  enabled: true,
  title: "Have a Project in Mind?",
  subtitle: "Let's collaborate and bring your ideas to life with modern technology and best practices.",
  backgroundStyle: "minimal", // "gradient", "pattern", "minimal"
  primaryAction: {
    text: "Get In Touch",
    link: "/contact"
  },
  secondaryAction: {
    text: "Read My Blog",
    link: "/blog"
  }
}
```

## 🔄 Konfigurasi Project Items

Project items dikonfigurasi dalam array `projects` di file yang sama:

```typescript
projects: [
  {
    id: "project-id",
    title: "Project Title",
    description: "Project description text",
    image: null, // Path ke gambar atau null
    tags: ["Tag1", "Tag2"],
    link: "https://project-link.com",
    github: "https://github.com/username/repo",
    featured: true, // Tampilkan di featured section
    category: "Cloud", // Kategori project
    status: "Active", // "Active", "Completed", "Archived"
    year: "2024"
  },
  // ... project lainnya
]
```

## 🎨 Optimasi CSS

Proyek ini mengimplementasikan optimasi CSS untuk halaman yang disabled. Ketika halaman dinonaktifkan melalui konfigurasi, CSS untuk halaman tersebut tidak akan dirender, mengurangi ukuran bundle CSS.

Lihat file [CSS_OPTIMIZATION.md](./CSS_OPTIMIZATION.md) untuk detail lengkap tentang optimasi CSS.

### Contoh: Disable Halaman dan CSS-nya

```typescript
// src/config/components.ts
pages: {
  about: {
    enabled: false // Set ke false untuk disable halaman dan CSS-nya
  }
}
```

## 🔍 Validasi

Saat build, sistem akan memvalidasi konfigurasi dan hanya mem-build halaman yang enabled. Halaman yang disabled akan mengembalikan 404 jika diakses langsung.
