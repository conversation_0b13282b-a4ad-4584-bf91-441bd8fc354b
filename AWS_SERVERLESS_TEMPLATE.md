# 🚀 AWS Serverless REST API - Template Project

## 📁 Project Structure
```
aws-serverless-api/
├── src/
│   ├── handlers/
│   │   ├── users.js          # User CRUD operations
│   │   ├── auth.js           # Authentication logic
│   │   └── health.js         # Health check endpoint
│   ├── models/
│   │   ├── user.js           # User data model
│   │   └── response.js       # API response helper
│   ├── utils/
│   │   ├── jwt.js            # JWT token utilities
│   │   ├── validator.js      # Input validation
│   │   └── logger.js         # Logging utility
│   └── config/
│       └── database.js       # DynamoDB configuration
├── infrastructure/
│   ├── template.yaml         # SAM template
│   ├── parameters.json       # Environment parameters
│   └── policies/
│       └── lambda-policy.json
├── tests/
│   ├── unit/
│   └── integration/
├── docs/
│   ├── api-spec.yaml         # OpenAPI specification
│   └── architecture.md
├── .github/
│   └── workflows/
│       └── deploy.yml        # CI/CD pipeline
├── package.json
├── README.md
└── .gitignore
```

## 🔧 Core Files Content

### 1. SAM Template (infrastructure/template.yaml)
```yaml
AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Serverless REST API for User Management

Globals:
  Function:
    Timeout: 30
    Runtime: nodejs18.x
    Environment:
      Variables:
        TABLE_NAME: !Ref UsersTable
        JWT_SECRET: !Ref JWTSecret

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]

Resources:
  # API Gateway
  ApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key'"
        AllowOrigin: "'*'"

  # Lambda Functions
  UsersFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/
      Handler: handlers/users.handler
      Events:
        GetUsers:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users
            Method: get
        CreateUser:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users
            Method: post

  # DynamoDB Table
  UsersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${Environment}-users"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH

  # Secrets Manager for JWT
  JWTSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Description: JWT Secret for API authentication
      GenerateSecretString:
        SecretStringTemplate: '{}'
        GenerateStringKey: 'secret'
        PasswordLength: 32
        ExcludeCharacters: '"@/\'

Outputs:
  ApiUrl:
    Description: "API Gateway endpoint URL"
    Value: !Sub "https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Environment}"
  
  UsersTableName:
    Description: "DynamoDB table name"
    Value: !Ref UsersTable
```

### 2. User Handler (src/handlers/users.js)
```javascript
const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');
const { validateUser, createResponse } = require('../utils');

const dynamodb = new AWS.DynamoDB.DocumentClient();
const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    try {
        const { httpMethod, pathParameters, body } = event;
        
        switch (httpMethod) {
            case 'GET':
                return await getUsers(pathParameters);
            case 'POST':
                return await createUser(JSON.parse(body));
            case 'PUT':
                return await updateUser(pathParameters, JSON.parse(body));
            case 'DELETE':
                return await deleteUser(pathParameters);
            default:
                return createResponse(405, { message: 'Method not allowed' });
        }
    } catch (error) {
        console.error('Error:', error);
        return createResponse(500, { message: 'Internal server error' });
    }
};

async function getUsers(pathParameters) {
    if (pathParameters && pathParameters.userId) {
        // Get single user
        const params = {
            TableName: TABLE_NAME,
            Key: { userId: pathParameters.userId }
        };
        
        const result = await dynamodb.get(params).promise();
        
        if (!result.Item) {
            return createResponse(404, { message: 'User not found' });
        }
        
        return createResponse(200, result.Item);
    } else {
        // Get all users
        const params = { TableName: TABLE_NAME };
        const result = await dynamodb.scan(params).promise();
        
        return createResponse(200, {
            users: result.Items,
            count: result.Count
        });
    }
}

async function createUser(userData) {
    // Validate input
    const validation = validateUser(userData);
    if (!validation.isValid) {
        return createResponse(400, { 
            message: 'Validation failed',
            errors: validation.errors 
        });
    }
    
    const user = {
        userId: uuidv4(),
        ...userData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    
    const params = {
        TableName: TABLE_NAME,
        Item: user
    };
    
    await dynamodb.put(params).promise();
    
    return createResponse(201, user);
}
```

### 3. Package.json
```json
{
  "name": "aws-serverless-api",
  "version": "1.0.0",
  "description": "Serverless REST API with AWS Lambda and DynamoDB",
  "main": "src/handlers/users.js",
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "deploy": "sam deploy --guided",
    "build": "sam build",
    "local": "sam local start-api"
  },
  "dependencies": {
    "aws-sdk": "^2.1490.0",
    "uuid": "^9.0.1",
    "jsonwebtoken": "^9.0.2"
  },
  "devDependencies": {
    "jest": "^29.7.0",
    "@types/jest": "^29.5.8"
  }
}
```

## 🚀 Quick Start Commands

```bash
# 1. Setup project
git clone <your-repo>
cd aws-serverless-api
npm install

# 2. Build and test locally
sam build
sam local start-api

# 3. Deploy to AWS
sam deploy --guided

# 4. Test API
curl https://your-api-url/dev/users
```

## 📊 Expected Learning Outcomes

Setelah menyelesaikan project ini, Anda akan memahami:
- ✅ Serverless architecture patterns
- ✅ AWS Lambda best practices
- ✅ DynamoDB operations
- ✅ API Gateway configuration
- ✅ Infrastructure as Code dengan SAM
- ✅ CI/CD untuk serverless applications
- ✅ Security dengan JWT dan IAM
- ✅ Monitoring dan logging

Project ini akan menjadi foundation yang solid untuk portfolio cloud engineering Anda!
