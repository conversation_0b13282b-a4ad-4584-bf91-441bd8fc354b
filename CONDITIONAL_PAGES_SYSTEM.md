# Conditional Pages System

Sistem yang memungkinkan halaman-halaman dapat di-enable/disable melal<PERSON> konfigurasi, dan secara otomatis mengatur rendering, build, sitemap, dan navigasi.

## 🎯 Fitur Utama

### **Smart Page Management**
- **Conditional Building**: <PERSON><PERSON> yang disabled tidak akan di-build sama sekali
- **Dynamic Sitemap**: <PERSON><PERSON> halaman yang enabled yang masuk sitemap
- **Smart Navigation**: Menu navigasi otomatis menyembunyikan halaman yang disabled
- **Section Dependencies**: About section bergantung pada about page being enabled

## 📁 File yang Terlibat

### **1. Configuration**
- `src/config/components.ts` - Konfigurasi utama pages
- `src/utils/pages.ts` - Utility functions untuk page management

### **2. Pages dengan Conditional Rendering**
- `src/pages/about.astro` - About page
- `src/pages/projects.astro` - Projects page  
- `src/pages/contact.astro` - Contact page
- `src/pages/index.astro` - Homepage (untuk sections)

### **3. Components yang Terpengaruh**
- `src/components/Header.tsx` - Navigation menu
- `src/utils/sitemap.ts` - Sitemap generation

## ⚙️ Cara Kerja

### **1. Page Configuration**
```typescript
// src/config/components.ts
pages: {
  about: {
    enabled: true,        // Enable/disable page
    showInNavigation: true // Show in navigation menu
  },
  projects: {
    enabled: true,
    showInNavigation: true
  },
  contact: {
    enabled: true,
    showInNavigation: true
  }
}
```

### **2. Page Validation**
Setiap halaman menggunakan `validatePageAccess()`:

```typescript
// src/pages/about.astro
try {
  validatePageAccess('about');
} catch (error) {
  // Page disabled, return 404
  return new Response(null, {
    status: 404,
    statusText: 'Page not found'
  });
}
```

### **3. Navigation Conditional Rendering**
```typescript
// src/components/Header.tsx
{pages.about.enabled && pages.about.showInNavigation && (
  <a href="/about">About</a>
)}
```

### **4. Section Dependencies**
```typescript
// src/utils/pages.ts
export function shouldShowAboutSection(): boolean {
  return componentConfig.pages.about.enabled && componentConfig.about.enabled;
}
```

## 🔧 Utility Functions

### **Available Functions:**

```typescript
// Check if page is enabled
isPageEnabled('about') // boolean

// Check if should show in navigation
shouldShowInNavigation('about') // boolean

// Check if about section should show (depends on about page)
shouldShowAboutSection() // boolean

// Check if contact section should show
shouldShowContactSection() // boolean

// Validate page access (throws error if disabled)
validatePageAccess('about') // void | throws Error

// Get enabled pages for sitemap
getEnabledStaticPages() // Array<PageConfig>

// Get page metadata for enabled pages
getPageMetadata() // Object
```

## 📊 Build Behavior

### **When Page is Enabled (`enabled: true`)**
```
✓ Page builds normally
✓ Appears in sitemap
✓ Shows in navigation (if showInNavigation: true)
✓ Related sections show on homepage
```

### **When Page is Disabled (`enabled: false`)**
```
❌ Page returns 404 (file not created, response body was empty)
❌ Excluded from sitemap
❌ Hidden from navigation
❌ Related sections hidden from homepage
```

## 🎯 Example Build Output

Ketika about page disabled:
```bash
▶ src/pages/about.astro
  └─ /about/index.html (+1ms) (file not created, response body was empty)
```

## 🛠 Cara Menggunakan

### **1. Disable About Page**
```typescript
// src/config/components.ts
pages: {
  about: {
    enabled: false,        // ← Disable about page
    showInNavigation: false
  }
}
```

**Hasil:**
- ❌ `/about` tidak di-build
- ❌ About tidak muncul di navigation
- ❌ About section tidak muncul di homepage
- ❌ About tidak masuk sitemap

### **2. Enable About Page tapi Hide dari Navigation**
```typescript
pages: {
  about: {
    enabled: true,         // ← Page tetap di-build
    showInNavigation: false // ← Tapi tidak muncul di menu
  }
}
```

**Hasil:**
- ✅ `/about` di-build dan bisa diakses
- ❌ About tidak muncul di navigation
- ✅ About section muncul di homepage
- ✅ About masuk sitemap

### **3. Enable Semua**
```typescript
pages: {
  about: {
    enabled: true,
    showInNavigation: true
  }
}
```

**Hasil:**
- ✅ `/about` di-build dan bisa diakses
- ✅ About muncul di navigation
- ✅ About section muncul di homepage
- ✅ About masuk sitemap

## 🔄 Dependencies

### **About Section Dependencies**
About section di homepage bergantung pada:
1. `pages.about.enabled` - About page harus enabled
2. `about.enabled` - About section config harus enabled

Jadi jika about page disabled, about section otomatis tidak muncul di homepage.

### **Contact Section Dependencies**
Contact section di homepage bergantung pada:
1. `pages.contact.enabled` - Contact page harus enabled
2. `contact.enabled` - Contact section config harus enabled

## 📝 Best Practices

### **1. Konsistensi**
Jika page disabled, pastikan semua referensi ke page tersebut juga dihilangkan.

### **2. SEO Friendly**
Pages yang disabled mengembalikan 404, bukan redirect, sehingga tidak di-index search engine.

### **3. Performance**
Pages yang disabled tidak di-build sama sekali, menghemat build time dan ukuran bundle.

### **4. User Experience**
Navigation otomatis menyembunyikan link ke pages yang disabled.

## 🚀 Keuntungan

1. **Flexible Configuration**: Easy enable/disable pages
2. **Build Optimization**: Disabled pages tidak di-build
3. **SEO Optimized**: Proper 404 handling
4. **Navigation Sync**: Menu otomatis update
5. **Sitemap Sync**: Sitemap otomatis update
6. **Section Dependencies**: Smart section rendering
7. **Performance**: Reduced bundle size
8. **Maintainability**: Centralized configuration

## 🔍 Testing

Untuk test sistem ini:

1. **Disable about page**:
   ```typescript
   pages: { about: { enabled: false } }
   ```

2. **Build project**:
   ```bash
   npm run build
   ```

3. **Check build output**:
   - About page should show: `(file not created, response body was empty)`
   - Navigation should not show About link
   - Homepage should not show About section
   - Sitemap should not include `/about`

4. **Enable about page** dan test lagi untuk memastikan semuanya muncul kembali.

Sistem ini memberikan kontrol penuh atas halaman mana yang di-render, di-build, dan ditampilkan kepada user, semuanya melalui konfigurasi sederhana.
