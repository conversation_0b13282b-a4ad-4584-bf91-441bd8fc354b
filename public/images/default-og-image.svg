<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Decorative Elements -->
  <circle cx="100" cy="100" r="50" fill="rgba(255,255,255,0.1)"/>
  <circle cx="1100" cy="530" r="80" fill="rgba(255,255,255,0.05)"/>
  <rect x="900" y="50" width="200" height="200" rx="20" fill="rgba(255,255,255,0.05)" transform="rotate(15 1000 150)"/>
  
  <!-- Main Content -->
  <g transform="translate(100, 150)">
    <!-- Title -->
    <text x="0" y="80" font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="64" font-weight="bold" fill="url(#textGradient)">
      Febryan Portfolio
    </text>
    
    <!-- Subtitle -->
    <text x="0" y="140" font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="32" font-weight="500" fill="rgba(255,255,255,0.9)">
      Cloud Engineer &amp; DevOps Specialist
    </text>
    
    <!-- Description -->
    <text x="0" y="200" font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="24" fill="rgba(255,255,255,0.8)">
      Sharing tutorials and insights about modern technology
    </text>
    
    <!-- Tech Stack Icons (simplified) -->
    <g transform="translate(0, 250)">
      <!-- Cloud Icon -->
      <rect x="0" y="0" width="60" height="40" rx="20" fill="rgba(255,255,255,0.2)"/>
      <text x="30" y="28" font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="16" text-anchor="middle" fill="white">☁️</text>
      
      <!-- Code Icon -->
      <rect x="80" y="0" width="60" height="40" rx="20" fill="rgba(255,255,255,0.2)"/>
      <text x="110" y="28" font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="16" text-anchor="middle" fill="white">💻</text>
      
      <!-- Automation Icon -->
      <rect x="160" y="0" width="60" height="40" rx="20" fill="rgba(255,255,255,0.2)"/>
      <text x="190" y="28" font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="16" text-anchor="middle" fill="white">⚙️</text>
      
      <!-- Infrastructure Icon -->
      <rect x="240" y="0" width="60" height="40" rx="20" fill="rgba(255,255,255,0.2)"/>
      <text x="270" y="28" font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="16" text-anchor="middle" fill="white">🏗️</text>
    </g>
  </g>
  
  <!-- Website URL -->
  <text x="100" y="580" font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="20" fill="rgba(255,255,255,0.7)">
    febryan.web.id
  </text>
  
  <!-- Logo/Avatar placeholder -->
  <circle cx="1000" cy="400" r="60" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
  <text x="1000" y="415" font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="48" text-anchor="middle" fill="white">F</text>
</svg>